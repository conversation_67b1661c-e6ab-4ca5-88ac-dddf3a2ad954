generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("MONGODB_URI")
}

model Resort {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  slug        String    @unique
  description String
  image       String
  location    String
  rooms       Room[]
  bookings    Booking[]
  reviews     Review[]
  createdAt   DateTime  @default(now())
}

model Room {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  resortId    String   @db.ObjectId
  name        String
  type        String
  description String
  price       Float
  image       String
  images      String[] @default([]) // Multiple images
  amenities   String[] @default([]) // Room amenities
  capacity    Int      @default(2) // Number of guests
  size        Float?   // Room size in square meters
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  resort   Resort    @relation(fields: [resortId], references: [id])
  bookings Booking[]
  reviews  Review[]
}

model SpaTreatment {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  price       Float
  duration    Int?      // Optional duration in minutes
  image       String
  category    String?   // Service category (Grooming, Wellness, Beauty, etc.)
  features    String[]  @default([]) // Array of service features
  isActive    Boolean   @default(true) // Whether the service is active/available
  bookings    Booking[]
  reviews     Review[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// Experience/Activities Model
model Experience {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  price       Float
  duration    Int?     // Duration in minutes
  image       String
  images      String[] @default([]) // Multiple images
  category    String?  // Adventure, Cultural, Water Sports, etc.
  features    String[] @default([]) // What's included
  difficulty  String?  // Easy, Moderate, Challenging
  minAge      Int?     // Minimum age requirement
  maxCapacity Int?     // Maximum participants
  location    String?  // Where the experience takes place
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  bookings    Booking[]
  reviews     Review[]
}

// Wellness Services Model
model WellnessService {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  price       Float
  duration    Int?     // Duration in minutes
  image       String
  images      String[] @default([]) // Multiple images
  category    String?  // Fitness, Yoga, Meditation, Therapy, etc.
  features    String[] @default([]) // What's included
  instructor  String?  // Instructor name
  equipment   String[] @default([]) // Required equipment
  maxCapacity Int?     // Maximum participants
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  bookings    Booking[]
  reviews     Review[]
}

// Dining Options Model
model DiningOption {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  image       String
  images      String[] @default([]) // Multiple images
  category    String?  // Restaurant, Cafe, Bar, Room Service, etc.
  cuisine     String?  // Ethiopian, International, Italian, etc.
  priceRange  String?  // $, $$, $$$, $$$$
  features    String[] @default([]) // Outdoor seating, Live music, etc.
  openingHours String? // Operating hours
  location    String?  // Location within resort
  capacity    Int?     // Seating capacity
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Events Model
model Event {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  price       Float?   // Price per person or base price
  image       String
  images      String[] @default([]) // Multiple images
  category    String?  // Wedding, Conference, Birthday, Corporate, etc.
  features    String[] @default([]) // What's included
  venue       String?  // Event venue/location
  maxCapacity Int?     // Maximum attendees
  duration    Int?     // Duration in hours
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  bookings    Booking[]
  reviews     Review[]
}

// Special Offers Model
model SpecialOffer {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  image       String
  images      String[]  @default([]) // Multiple images
  category    String?   // Package, Discount, Seasonal, etc.
  originalPrice Float?  // Original price
  discountPrice Float?  // Discounted price
  discountPercent Int?  // Discount percentage
  features    String[]  @default([]) // What's included
  validFrom   DateTime? // Offer start date
  validUntil  DateTime? // Offer end date
  terms       String?   // Terms and conditions
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  role          String    @default("user") // 'admin', 'manager', 'receptionist', 'user'
  accounts      Account[]
  sessions      Session[]
  reviews       Review[]
  testimonials  Testimonial[]
  createdAt     DateTime  @default(now())
}

model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Booking {
  id           String        @id @default(auto()) @map("_id") @db.ObjectId
  userEmail    String        // Consider adding User relation in future

  // Content type IDs (only one should be set per booking)
  resortId     String?       @db.ObjectId
  roomId       String?       @db.ObjectId
  spaId        String?       @db.ObjectId
  experienceId String?       @db.ObjectId
  wellnessId   String?       @db.ObjectId
  eventId      String?       @db.ObjectId

  checkIn      DateTime
  checkOut     DateTime?
  notes        String?       @default("")
  status       BookingStatus @default(PENDING)
  totalAmount  Float?        // Total booking amount
  participants Int?          // Number of participants
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  resort          Resort?          @relation(fields: [resortId], references: [id], onDelete: Cascade)
  room            Room?            @relation(fields: [roomId], references: [id], onDelete: Cascade)
  spaTreatment    SpaTreatment?    @relation(fields: [spaId], references: [id], onDelete: Cascade)
  experience      Experience?      @relation(fields: [experienceId], references: [id], onDelete: Cascade)
  wellnessService WellnessService? @relation(fields: [wellnessId], references: [id], onDelete: Cascade)
  event           Event?           @relation(fields: [eventId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([userEmail])
  @@index([checkIn])
  @@index([status])
  @@index([resortId, checkIn])
  @@index([roomId, checkIn])
  @@index([spaId, checkIn])
  @@index([experienceId, checkIn])
  @@index([wellnessId, checkIn])
  @@index([eventId, checkIn])
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
}

// Review Model - For guest reviews of services/resorts
model Review {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @db.ObjectId

  // Content type IDs (only one should be set per review)
  resortId     String?  @db.ObjectId
  roomId       String?  @db.ObjectId
  spaId        String?  @db.ObjectId
  experienceId String?  @db.ObjectId
  wellnessId   String?  @db.ObjectId
  eventId      String?  @db.ObjectId

  // Review content
  rating      Int      // 1-5 stars
  title       String?  // Optional review title
  comment     String   // Review text
  images      String[] @default([]) // Optional review images

  // Verification and moderation
  isVerified  Boolean  @default(false) // Verified guest (has booking)
  isApproved  Boolean  @default(false) // Admin approved
  isPublished Boolean  @default(false) // Published on site

  // Engagement metrics
  helpfulVotes Int     @default(0)
  reportCount  Int     @default(0)

  // Relations
  user         User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  resort       Resort?           @relation(fields: [resortId], references: [id], onDelete: Cascade)
  room         Room?             @relation(fields: [roomId], references: [id], onDelete: Cascade)
  spaTreatment SpaTreatment?     @relation(fields: [spaId], references: [id], onDelete: Cascade)
  experience   Experience?       @relation(fields: [experienceId], references: [id], onDelete: Cascade)
  wellnessService WellnessService? @relation(fields: [wellnessId], references: [id], onDelete: Cascade)
  event        Event?            @relation(fields: [eventId], references: [id], onDelete: Cascade)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Indexes for performance
  @@index([userId])
  @@index([rating])
  @@index([isPublished])
  @@index([isApproved])
  @@index([resortId, isPublished])
  @@index([spaId, isPublished])
}

// Testimonial Model - For featured guest testimonials
model Testimonial {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String?  @db.ObjectId // Optional - can be anonymous

  // Testimonial content
  name        String   // Guest name (can be anonymous like "John D.")
  title       String?  // Optional title/headline
  content     String   // Testimonial text
  image       String?  // Optional guest photo
  location    String?  // Guest location (e.g., "Addis Ababa, Ethiopia")

  // Display settings
  isFeatured  Boolean  @default(false) // Featured on homepage
  isApproved  Boolean  @default(false) // Admin approved
  isPublished Boolean  @default(false) // Published on site
  displayOrder Int?    // Order for featured testimonials

  // Relations
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Indexes for performance
  @@index([isPublished])
  @@index([isFeatured])
  @@index([displayOrder])
}
