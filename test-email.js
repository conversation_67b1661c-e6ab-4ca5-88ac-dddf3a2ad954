const nodemailer = require("nodemailer");
require("dotenv").config();

// Test email configuration
async function testEmailConfig() {
  console.log("🔍 Testing email configuration...");

  // Check environment variables
  console.log("\n📋 Environment Variables:");
  console.log("EMAIL_HOST:", process.env.EMAIL_HOST || "NOT SET");
  console.log("EMAIL_PORT:", process.env.EMAIL_PORT || "NOT SET");
  console.log("EMAIL_SECURE:", process.env.EMAIL_SECURE || "NOT SET");
  console.log("EMAIL_USER:", process.env.EMAIL_USER ? "✅ SET" : "❌ NOT SET");
  console.log("EMAIL_PASS:", process.env.EMAIL_PASS ? "✅ SET" : "❌ NOT SET");

  // Create transporter
  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: Number(process.env.EMAIL_PORT),
    secure: process.env.EMAIL_SECURE === "true",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    tls: {
      rejectUnauthorized: true,
    },
  });

  try {
    console.log("\n🔐 Verifying SMTP connection...");
    await transporter.verify();
    console.log("✅ Email configuration verified successfully!");

    // Test sending a simple email
    console.log("\n📧 Sending test email...");
    const testEmail = {
      from: `"Kuriftu Resorts Test" <${process.env.EMAIL_USER}>`,
      to: process.env.EMAIL_USER, // Send to self for testing
      subject: "Email Configuration Test - Kuriftu Resorts",
      html: `
        <h2>Email Configuration Test</h2>
        <p>This is a test email to verify that the email configuration is working correctly.</p>
        <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
        <p><strong>Configuration:</strong></p>
        <ul>
          <li>Host: ${process.env.EMAIL_HOST}</li>
          <li>Port: ${process.env.EMAIL_PORT}</li>
          <li>Secure: ${process.env.EMAIL_SECURE}</li>
        </ul>
      `,
    };

    const info = await transporter.sendMail(testEmail);
    console.log("✅ Test email sent successfully!");
    console.log("Message ID:", info.messageId);
  } catch (error) {
    console.error("❌ Email configuration test failed:", error.message);

    // Provide specific error guidance
    if (error.code === "EAUTH") {
      console.log("\n💡 Authentication Error Solutions:");
      console.log("1. Verify EMAIL_USER and EMAIL_PASS are correct");
      console.log("2. For Gmail, use App Password instead of regular password");
      console.log(
        "3. Enable 2-factor authentication and generate App Password"
      );
      console.log(
        '4. Check if "Less secure app access" is enabled (not recommended)'
      );
    } else if (error.code === "ECONNECTION") {
      console.log("\n💡 Connection Error Solutions:");
      console.log("1. Check EMAIL_HOST and EMAIL_PORT settings");
      console.log("2. Verify internet connection");
      console.log("3. Check firewall settings");
    } else if (error.code === "ESOCKET") {
      console.log("\n💡 Socket Error Solutions:");
      console.log(
        "1. Check EMAIL_SECURE setting (true for port 465, false for port 587)"
      );
      console.log("2. Try different port (587 for STARTTLS, 465 for SSL)");
    }
  }
}

// Run the test
testEmailConfig().catch(console.error);
